# Graph Configuration Module - Demo Instructions

## 🚀 How to Run the Demo

Since R is not available in the terminal, here's how to run the demo application:

### Option 1: Run in RStudio
1. Open RStudio
2. Set working directory to this project folder
3. Run: `source("demo_app.R")`
4. The Shiny app will open in your browser

### Option 2: Run in R Console
```r
# Set working directory
setwd("/Users/<USER>/Desktop/Projects/CausalKnowledgeTrace")

# Run the demo app
source("demo_app.R")
```

## 📋 Step-by-Step Demo Instructions

### Step 1: Navigate to Graph Configuration Tab
- When the app opens, click on "Graph Configuration" in the sidebar
- You'll see the complete configuration form

### Step 2: Fill in Exposure CUIs
**Field:** Exposure CUIs (required)
**Example Input:**
```
C0011849, C0020538
```
**What it means:** These are UMLS Concept Unique Identifiers for exposure variables
**Format:** Each CUI must be C followed by exactly 7 digits

### Step 3: Fill in Outcome CUIs  
**Field:** Outcome CUIs (required)
**Example Input:**
```
C0027051, C0038454
```
**What it means:** UMLS Concept Unique Identifiers for outcome variables
**Format:** Same as exposure CUIs - C followed by exactly 7 digits

### Step 4: Configure Parameters

#### Min PMIDs
**Field:** Minimum PMIDs dropdown
**Example Selection:** 100 (default)
**Options:** 10, 25, 50, 100, 250, 500, 1000, 2000, 5000
**What it means:** Minimum number of PubMed IDs required for inclusion

#### Publication Year Cutoff
**Field:** Publication Year Cutoff dropdown  
**Example Selection:** 2010 (default)
**Options:** 2000, 2005, 2010, 2015, 2020
**What it means:** Only include publications from this year onwards

#### Squelch Threshold
**Field:** Squelch Threshold dropdown
**Example Selection:** 50 (default)
**Options:** 10, 25, 50, 100, 500
**What it means:** Threshold for filtering low-frequency relationships

#### K-Hops
**Field:** K-Hops dropdown
**Example Selection:** 2 (default)
**Options:** 1, 2, 3
**What it means:** Number of relationship hops in graph traversal

#### SemMedDB Version
**Field:** SemMedDB Version dropdown
**Example Selection:** heuristic (default)
**Options:** heuristic, LLM-based, heuristic+LLM-based
**What it means:** Processing method for SemMedDB

### Step 5: Optional - Predication Types
**Field:** Predication Types (optional text input)
**Example Input:**
```
TREATS, CAUSES, PREVENTS
```
**What it means:** Specific relationship types to include
**Note:** Leave empty to include all relationship types

### Step 6: Create Graph Configuration
1. Click the blue "Create Graph" button
2. The system will:
   - Validate all your inputs
   - Show any validation errors if found
   - Save configuration to `user_input.yaml` if valid
   - Display success message

## 🎯 Complete Example Configuration

Here's a complete set of inputs you can use for testing:

```
Exposure CUIs: C0011849, C0020538
Outcome CUIs: C0027051, C0038454
Min PMIDs: 100
Publication Year Cutoff: 2010
Squelch Threshold: 50
K-Hops: 2
Predication Types: TREATS, CAUSES
SemMedDB Version: heuristic
```

This will generate the following `user_input.yaml` file:

```yaml
exposure_cuis: 
  - C0011849
  - C0020538
outcome_cuis: 
  - C0027051
  - C0038454
min_pmids: 100
pub_year_cutoff: 2010
squelch_threshold: 50
k_hops: 2
PREDICATION_TYPE: "TREATS, CAUSES"
SemMedDBD_version: "heuristic"
```

## ⚠️ Common Validation Errors and Solutions

### Error: "Invalid CUI format"
**Problem:** CUI doesn't follow C0000000 format
**Solution:** Ensure each CUI is C followed by exactly 7 digits
**Example:** 
- ❌ Wrong: `C001184, INVALID, C12345678`
- ✅ Correct: `C0011849, C0020538`

### Error: "CUI field cannot be empty"
**Problem:** Required CUI fields are empty
**Solution:** Enter at least one valid CUI in both exposure and outcome fields

### Error: "Missing required fields"
**Problem:** Dropdown selections not made
**Solution:** Ensure all dropdown menus have selections (they should have defaults)

## 🔍 What to Look For

### Success Indicators
- ✅ Green success message appears
- ✅ "Configuration saved to user_input.yaml" notification
- ✅ Status box shows configuration details
- ✅ `user_input.yaml` file created in project directory

### Validation Feedback
- Real-time validation messages appear below the form
- Color-coded alerts (red for errors, green for success)
- Specific error messages for each validation rule

## 📁 Generated Files

After successful configuration:
- **`user_input.yaml`** - Main configuration file for downstream processing
- Configuration can be reloaded using `load_graph_config("user_input.yaml")`

## 🧪 Testing Different Scenarios

### Test 1: Valid Configuration
Use the complete example above - should succeed

### Test 2: Invalid CUI Format
Try: `C001184, INVALID` - should show validation error

### Test 3: Empty Required Fields
Leave CUI fields empty - should show required field error

### Test 4: Minimal Configuration
Use only required fields, leave Predication Types empty - should succeed

## 🎉 Next Steps

Once you successfully generate `user_input.yaml`:
1. Verify the file exists in your project directory
2. Check the file contents match your inputs
3. Use this configuration file in your downstream knowledge graph processing
4. Integrate the module into your main CausalKnowledgeTrace application

The demo application includes an "Instructions" tab with additional guidance and examples!
