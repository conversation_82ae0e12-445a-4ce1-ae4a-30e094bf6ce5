# DAG Data Configuration File
# This file contains the DAG structure and can be easily modified
# Replace the 'g' variable with your own dagitty graph

library(SEMgraph)
library(dagitty)
library(igraph)
library(visNetwork)
library(dplyr)

# Source modular components
source("dag_visualization.R")
source("node_information.R")
source("statistics.R")
source("data_upload.R")

# DYNAMIC DAG LOADING SYSTEM
# This system allows users to load DAG files through the UI
# NO AUTO-LOADING - Files are loaded only when user selects them

# Initialize variables - no automatic loading
g <- NULL
dag_loaded_from <- "none"
available_dag_files <- character(0)

# Use modular functions (these are now defined in data_upload.R)
# scan_for_dag_files and load_dag_from_file are available from data_upload.R

# Scan for available DAG files but don't auto-load them
available_dag_files <- scan_for_dag_files()

cat("Application starting without auto-loading DAG files.\n")
cat("Available DAG files detected:", if(length(available_dag_files) > 0) paste(available_dag_files, collapse = ", ") else "None", "\n")
cat("Use the file selection interface to load a graph.\n")

# No automatic loading - g remains NULL

# Use modular functions (these are now defined in data_upload.R and node_information.R)
# create_network_data and process_large_dag are available from data_upload.R

# Since g is NULL (no auto-loading), create placeholder data
if (is.null(g)) {
    cat("No DAG loaded. Creating placeholder data for initial app state.\n")

    # Create placeholder data
    dag_nodes <- data.frame(
        id = "Welcome",
        label = "Select a graph file to begin",
        group = "Placeholder",
        color = "#A9B7C0",
        font.size = 16,
        font.color = "black",
        stringsAsFactors = FALSE
    )

    dag_edges <- data.frame(
        from = character(0),
        to = character(0),
        arrows = character(0),
        smooth = logical(0),
        width = numeric(0),
        color = character(0),
        stringsAsFactors = FALSE
    )

    dag_object <- NULL

} else {
    # Process loaded DAG (this will only happen when explicitly loaded by user)
    tryCatch({
        network_data <- process_large_dag(g)

        # Export the data for the Shiny app
        dag_nodes <- network_data$nodes
        dag_edges <- network_data$edges
        dag_object <- network_data$dag

        # Validate the data
        if (nrow(dag_nodes) == 0) {
            warning("No nodes found in the DAG. Please check your dagitty syntax.")
            # Create minimal fallback data
            dag_nodes <- data.frame(
                id = c("Node1", "Node2"),
                label = c("Node 1", "Node 2"),
                group = c("Other", "Other"),
                color = c("#808080", "#808080"),
                font.size = 14,
                font.color = "black",
                stringsAsFactors = FALSE
            )
            dag_edges <- data.frame(
                from = "Node1",
                to = "Node2",
                arrows = "to",
                smooth = TRUE,
                width = 1,
                color = "#666666",
                stringsAsFactors = FALSE
            )
        }

        cat("Successfully processed DAG with", nrow(dag_nodes), "nodes and", nrow(dag_edges), "edges.\n")
        cat("DAG loaded from:", dag_loaded_from, "\n")

    }, error = function(e) {
        cat("Error processing DAG:", e$message, "\n")
        cat("Creating minimal fallback data...\n")

        # Create minimal fallback data
        dag_nodes <- data.frame(
            id = c("Error", "Fallback"),
            label = c("Error Node", "Fallback Node"),
            group = c("Other", "Other"),
            color = c("#FF0000", "#808080"),
            font.size = 14,
            font.color = "black",
            stringsAsFactors = FALSE
        )

        dag_edges <- data.frame(
            from = "Error",
            to = "Fallback",
            arrows = "to",
            smooth = TRUE,
            width = 1,
            color = "#666666",
            stringsAsFactors = FALSE
        )

        dag_object <- NULL
    })
}

# Clean up intermediate variables
rm(network_data)
if (exists("g")) {
    cat("DAG loaded successfully. You can now run the Shiny app.\n")
}