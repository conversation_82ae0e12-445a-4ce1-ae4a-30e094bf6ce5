# Test script to verify the application structure
# This script checks if the modified app can be loaded without errors

cat("Testing modified Shiny application structure...\n")

# Test 1: Check if required libraries can be loaded
cat("Test 1: Loading required libraries...\n")
tryCatch({
    library(shiny)
    library(shinydashboard)
    library(visNetwork)
    library(dplyr)
    library(DT)
    cat("✓ All required libraries loaded successfully\n")
}, error = function(e) {
    cat("✗ Error loading libraries:", e$message, "\n")
    stop("Cannot proceed without required libraries")
})

# Test 2: Check if modular components can be sourced
cat("\nTest 2: Loading modular components...\n")
modules <- c("dag_visualization.R", "node_information.R", "statistics.R", "data_upload.R")
for (module in modules) {
    tryCatch({
        source(module)
        cat("✓", module, "loaded successfully\n")
    }, error = function(e) {
        cat("✗ Error loading", module, ":", e$message, "\n")
    })
}

# Test 3: Check if dag_data.R can be sourced (should not auto-load files)
cat("\nTest 3: Testing dag_data.R (should create placeholder data)...\n")
tryCatch({
    source("dag_data.R")
    if (exists("dag_nodes") && exists("dag_edges")) {
        cat("✓ dag_data.R loaded successfully\n")
        cat("  - Nodes created:", nrow(dag_nodes), "\n")
        cat("  - Edges created:", nrow(dag_edges), "\n")
        cat("  - DAG loaded from:", dag_loaded_from, "\n")
        
        # Check if it's placeholder data
        if (dag_loaded_from == "none" && nrow(dag_nodes) == 1 && dag_nodes$id[1] == "Welcome") {
            cat("✓ Correctly created placeholder data (no auto-loading)\n")
        } else {
            cat("⚠ Warning: Expected placeholder data, but got different data\n")
        }
    } else {
        cat("✗ dag_nodes or dag_edges not created\n")
    }
}, error = function(e) {
    cat("✗ Error loading dag_data.R:", e$message, "\n")
})

# Test 4: Check if app.R can be parsed (syntax check)
cat("\nTest 4: Checking app.R syntax...\n")
tryCatch({
    # Parse the file to check for syntax errors
    parse("app.R")
    cat("✓ app.R syntax is valid\n")
}, error = function(e) {
    cat("✗ Syntax error in app.R:", e$message, "\n")
})

# Test 5: Check if file scanning works
cat("\nTest 5: Testing file scanning functionality...\n")
tryCatch({
    available_files <- scan_for_dag_files()
    cat("✓ File scanning works\n")
    cat("  - Available DAG files:", length(available_files), "\n")
    if (length(available_files) > 0) {
        cat("  - Files found:", paste(available_files, collapse = ", "), "\n")
    }
}, error = function(e) {
    cat("✗ Error in file scanning:", e$message, "\n")
})

# Test 6: Test loading a sample file
cat("\nTest 6: Testing DAG file loading...\n")
if (file.exists("consolidated.R")) {
    tryCatch({
        result <- load_dag_from_file("consolidated.R")
        if (result$success) {
            cat("✓ Successfully loaded consolidated.R\n")
            cat("  - Message:", result$message, "\n")
        } else {
            cat("✗ Failed to load consolidated.R:", result$message, "\n")
        }
    }, error = function(e) {
        cat("✗ Error loading consolidated.R:", e$message, "\n")
    })
} else {
    cat("⚠ consolidated.R not found, skipping load test\n")
}

cat("\n=== Test Summary ===\n")
cat("The application structure has been tested.\n")
cat("If all tests passed, the app should start without auto-loading files\n")
cat("and allow users to select files through the interface.\n")
