# Test script to verify DAG processing fixes
# This script tests the fixed DAG processing functionality

# Load required libraries
library(dagitty)
library(dplyr)

# Source the data upload module
source("data_upload.R")

cat("=== Testing DAG Processing Fixes ===\n\n")

# Test 1: Create a simple DAG
cat("Test 1: Creating and processing a simple DAG\n")
simple_dag <- dagitty('dag {
    X [exposure]
    Y [outcome]
    Z
    X -> Z -> Y
    X -> Y
}')

cat("Simple DAG created successfully\n")
cat("Nodes:", paste(names(simple_dag), collapse = ", "), "\n")

# Test the edges() function
tryCatch({
    edge_info <- edges(simple_dag)
    cat("Edges extracted:", length(edge_info), "edges\n")
    cat("Edge list:", paste(edge_info, collapse = ", "), "\n")
}, error = function(e) {
    cat("Error extracting edges:", e$message, "\n")
})

# Test 2: Validate the DAG
cat("\nTest 2: Validating DAG\n")
validation_result <- validate_dag_object(simple_dag)
cat("Validation result:", validation_result$valid, "\n")
cat("Message:", validation_result$message, "\n")

# Test 3: Create network data
cat("\nTest 3: Creating network data\n")
tryCatch({
    network_data <- create_network_data(simple_dag)
    cat("Network data created successfully\n")
    cat("Nodes:", nrow(network_data$nodes), "\n")
    cat("Edges:", nrow(network_data$edges), "\n")
    
    if (nrow(network_data$nodes) > 0) {
        cat("Node IDs:", paste(network_data$nodes$id, collapse = ", "), "\n")
    }
    
    if (nrow(network_data$edges) > 0) {
        cat("Edge connections:\n")
        for (i in 1:nrow(network_data$edges)) {
            cat("  ", network_data$edges$from[i], "->", network_data$edges$to[i], "\n")
        }
    }
    
}, error = function(e) {
    cat("Error creating network data:", e$message, "\n")
})

# Test 4: Test with a more complex DAG
cat("\nTest 4: Testing with a more complex DAG\n")
complex_dag <- dagitty('dag {
    A [exposure]
    B [outcome]
    C
    D
    E
    A -> C -> B
    A -> D -> B
    C -> E -> D
    A -> B
}')

tryCatch({
    complex_network_data <- create_network_data(complex_dag)
    cat("Complex network data created successfully\n")
    cat("Nodes:", nrow(complex_network_data$nodes), "\n")
    cat("Edges:", nrow(complex_network_data$edges), "\n")
}, error = function(e) {
    cat("Error with complex DAG:", e$message, "\n")
})

cat("\n=== Test Summary ===\n")
cat("DAG processing fixes test completed.\n")
cat("Check the output above for any errors.\n")
